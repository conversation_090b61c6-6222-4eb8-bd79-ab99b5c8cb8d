// Hamburger Button Fix - Ensure it works properly
console.log('🔧 HAMBURGER FIX: Loading hamburger fix...');

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 HAMBURGER FIX: DOM loaded, initializing hamburger fix...');
    initializeHamburgerFix();
});

// Also run immediately in case DOM is already loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeHamburgerFix);
} else {
    console.log('🔧 HAMBURGER FIX: DOM already loaded, running immediately...');
    initializeHamburgerFix();
}

function initializeHamburgerFix() {
    console.log('🔧 HAMBURGER FIX: Initializing hamburger fix...');

    // Get hamburger elements
    const menuToggle = document.getElementById('menu-toggle');
    const menuClose = document.getElementById('menu-close');
    const mobileMenu = document.getElementById('mobile-menu');
    const menuButton = document.querySelector('.mobile-menu-btn');

    console.log('🔧 HAMBURGER FIX: Elements found:', {
        menuToggle: !!menuToggle,
        menuClose: !!menuClose,
        mobileMenu: !!mobileMenu,
        menuButton: !!menuButton
    });

    // Log element details for debugging
    if (menuToggle) console.log('🔧 HAMBURGER FIX: Menu toggle element:', menuToggle);
    if (menuButton) console.log('🔧 HAMBURGER FIX: Menu button element:', menuButton);
    if (mobileMenu) console.log('🔧 HAMBURGER FIX: Mobile menu element:', mobileMenu);
    
    if (!menuToggle || !menuClose || !mobileMenu) {
        console.warn('Required hamburger elements not found');
        return;
    }
    
    // Ensure hamburger button is properly styled and visible
    if (menuButton) {
        menuButton.style.display = 'flex';
        menuButton.style.alignItems = 'center';
        menuButton.style.justifyContent = 'center';
        menuButton.style.cursor = 'pointer';
        menuButton.style.pointerEvents = 'auto';
        menuButton.style.visibility = 'visible';
        menuButton.style.opacity = '1';
    }
    
    // Remove any existing event listeners by cloning
    const newMenuToggle = menuToggle.cloneNode(true);
    const newMenuClose = menuClose.cloneNode(true);
    
    if (menuToggle.parentNode) {
        menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);
    }
    if (menuClose.parentNode) {
        menuClose.parentNode.replaceChild(newMenuClose, menuClose);
    }
    
    // Get fresh references
    const freshMenuToggle = document.getElementById('menu-toggle');
    const freshMenuClose = document.getElementById('menu-close');
    
    // Open menu function
    function openMobileMenu() {
        console.log('Opening mobile menu via hamburger fix');
        if (mobileMenu) {
            mobileMenu.classList.add('active');
            mobileMenu.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // Animate in
            setTimeout(() => {
                mobileMenu.style.top = '0';
            }, 10);
        }
    }
    
    // Close menu function
    function closeMobileMenu() {
        console.log('Closing mobile menu via hamburger fix');
        if (mobileMenu) {
            mobileMenu.style.top = '-100%';
            document.body.style.overflow = 'auto';
            
            setTimeout(() => {
                mobileMenu.classList.remove('active');
                mobileMenu.style.display = 'none';
            }, 400);
        }
    }
    
    // Add click events
    if (freshMenuToggle) {
        freshMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Hamburger clicked!');
            openMobileMenu();
        });
        
        // Also add to parent button
        if (menuButton) {
            menuButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Hamburger button clicked!');
                openMobileMenu();
            });
        }
    }
    
    if (freshMenuClose) {
        freshMenuClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Close button clicked!');
            closeMobileMenu();
        });
    }
    
    // Close on outside click
    document.addEventListener('click', function(e) {
        if (mobileMenu && mobileMenu.classList.contains('active')) {
            if (!mobileMenu.contains(e.target) && !menuButton.contains(e.target)) {
                closeMobileMenu();
            }
        }
    });
    
    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
            closeMobileMenu();
        }
    });
    
    console.log('Hamburger fix initialized successfully!');
}

// Export for global access
window.initializeHamburgerFix = initializeHamburgerFix;
