/* Migrate Page Styles */
body {
    font-family: 'Inter', sans-serif;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Hero Section */
.migrate-hero-section {
    position: relative;
    padding: 80px 0 100px;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(#3b82f6 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.floating-icon {
    position: absolute;
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.1);
    z-index: 2;
}

.floating-icon i {
    font-size: 24px;
    color: #000080;
}

.migrate-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.migrate-hero-content {
    display: flex;
    align-items: center;
    gap: 40px;
}

.hero-left {
    flex: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.badge-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-text {
    font-size: 0.9rem;
}

.hero-left h1 {
    font-size: 3rem;
    color: #000080;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 30px;
    max-width: 90%;
}

.hero-form {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.1);
}

.hero-form h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 20px;
}

.migrate-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    width: 100%;
}

.form-select,
.form-input {
    width: 100%;
    padding: 15px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    color: #334155;
    background: #f8fafc;
    transition: all 0.3s ease;
}

.form-select:focus,
.form-input:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-submit {
    width: 100%;
    padding: 15px;
    background: #000080;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.form-submit:hover {
    background: #3b82f6;
    transform: translateY(-3px);
}

.hero-right {
    flex: 1;
    position: relative;
}

.hero-image {
    width: 100%;
    height: 500px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 128, 0.15);
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.confused-card {
    position: absolute;
    bottom: -30px;
    left: -30px;
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.1);
    text-align: center;
    width: 200px;
}

.confused-icon {
    width: 50px;
    height: 50px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.confused-icon i {
    font-size: 24px;
    color: #3b82f6;
}

.confused-card h4 {
    font-size: 1.2rem;
    color: #000080;
    margin-bottom: 5px;
}

.confused-card p {
    font-size: 0.9rem;
    color: #64748b;
    margin-bottom: 15px;
}

.consult-btn {
    display: inline-block;
    padding: 10px 20px;
    background: #000080;
    color: white;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.consult-btn:hover {
    background: #3b82f6;
    transform: translateY(-3px);
}

/* Responsive styles */
@media (max-width: 992px) {
    .migrate-hero-content {
        flex-direction: column;
    }
    
    .hero-left, .hero-right {
        width: 100%;
    }
    
    .hero-left h1 {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        max-width: 100%;
    }
    
    .hero-image {
        height: 400px;
    }
    
    .confused-card {
        bottom: -20px;
        left: 20px;
    }
}

@media (max-width: 768px) {
    .migrate-hero-section {
        padding: 60px 0 80px;
    }
    
    .hero-left h1 {
        font-size: 2rem;
    }
    
    .hero-form {
        padding: 20px;
    }
    
    .hero-image {
        height: 300px;
    }
    
    .confused-card {
        position: relative;
        bottom: 0;
        left: 0;
        margin-top: 20px;
        width: 100%;
    }
}