/* Mobile Navigation Fix - Force Mobile Menu to Work Correctly */

/* Force hide desktop menu on mobile */
@media (max-width: 768px) {
    .desktop-menu {
        display: none !important;
    }
    
    /* Force show hamburger button */
    .mobile-menu-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 50px !important;
        height: 50px !important;
        cursor: pointer !important;
        position: absolute !important;
        right: 15px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 1001 !important;
        background: transparent !important;
        border-radius: 8px !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* Force show hamburger icon */
    .mobile-menu-btn i {
        font-size: 24px !important;
        color: #687FE5 !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* Fix mobile menu positioning */
    .mobile-menu {
        position: fixed !important;
        top: -100% !important;
        left: 0 !important;
        width: 100% !important;
        height: 100vh !important;
        background: white !important;
        z-index: 1000 !important;
        overflow-y: auto !important;
        transition: top 0.3s ease !important;
    }
    
    /* Fix mobile menu when active */
    .mobile-menu.active {
        top: 0 !important;
        display: block !important;
    }
    
    /* Fix mobile menu header */
    .mobile-menu-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 20px !important;
        border-bottom: 1px solid #eee !important;
    }
    
    /* Fix mobile menu close button */
    #menu-close {
        display: block !important;
        font-size: 24px !important;
        color: #687FE5 !important;
        cursor: pointer !important;
    }
    
    /* Fix mobile menu links */
    .mobile-nav-links {
        display: block !important;
        padding: 20px 0 !important;
    }
    
    /* Fix mobile menu apply button */
    .mobile-apply-btn {
        display: block !important;
        margin: 20px !important;
    }
}

/* Force desktop menu on larger screens */
@media (min-width: 769px) {
    .mobile-menu-btn {
        display: none !important;
    }
    
    .desktop-menu {
        display: flex !important;
    }
}