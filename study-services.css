/* Professional Study Services Section */
.study-services-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    position: relative;
    overflow: hidden;
}

.study-services-section::before {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: rgba(0, 0, 128, 0.05);
    border-radius: 50%;
    z-index: 1;
}

.study-services-section::after {
    content: "";
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: rgba(0, 0, 128, 0.05);
    border-radius: 50%;
    z-index: 1;
}

.study-services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.study-services-section .section-header {
    text-align: center;
    margin-bottom: 50px;
}

.study-services-section .section-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.study-services-section .section-header h2::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #3b82f6;
}

.study-services-section .section-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 10px;
    padding: 35px;
    transition: all 0.4s ease;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.08);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.service-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #000080 0%, #3b82f6 100%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.4s ease;
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(0, 0, 128, 0.15);
}

.service-card:hover::before {
    opacity: 1;
}

.service-card:hover h3,
.service-card:hover p,
.service-card:hover ul li {
    color: white;
}

.service-card:hover ul li::before {
    color: rgba(255, 255, 255, 0.8);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: #f0f9ff;
    color: #000080;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 25px;
    transition: all 0.4s ease;
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.1);
    position: relative;
    overflow: hidden;
}

.service-icon::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(45deg);
    opacity: 0;
    transition: all 0.6s ease;
}

.service-card:hover .service-icon {
    background: white;
    color: #000080;
    transform: rotate(10deg);
}

.service-card:hover .service-icon::after {
    opacity: 1;
    transform: rotate(45deg) translateY(-100%);
}

.service-card h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.service-card p {
    color: #64748b;
    margin-bottom: 20px;
    line-height: 1.6;
}

.service-card ul {
    list-style: none;
    padding: 0;
    margin: 0 0 25px;
}

.service-card ul li {
    color: #64748b;
    padding: 8px 0 8px 25px;
    position: relative;
    border-bottom: 1px dashed #e2e8f0;
}

.service-card ul li:last-child {
    border-bottom: none;
}

.service-card ul li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #3b82f6;
    font-weight: bold;
}

.service-btn {
    display: inline-block;
    background: transparent;
    color: #000080;
    padding: 12px 28px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s ease;
    border: 2px solid #000080;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.service-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: #000080;
    transition: all 0.4s ease;
    z-index: -1;
}

.service-btn:hover {
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.2);
}

.service-btn:hover::before {
    width: 100%;
}

.service-card:hover .service-btn {
    background: white;
    color: #000080;
    border-color: white;
}

.service-card:hover .service-btn:hover {
    background: transparent;
    color: white;
}

.service-card:hover .service-btn::before {
    background: white;
}

/* Responsive styles */
@media (max-width: 992px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .study-services-section .section-header h2 {
        font-size: 2rem;
    }
}