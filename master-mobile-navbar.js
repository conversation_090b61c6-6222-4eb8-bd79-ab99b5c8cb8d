// MASTER MOBILE NAVBAR JAVASCRIPT - Universal for All Pages

(function() {
    'use strict';
    
    console.log('🔥 MASTER MOBILE NAVBAR: Loading...');
    
    function initMasterMobileNavbar() {
        console.log('🔥 MASTER MOBILE NAVBAR: Initializing...');
        
        // Get all required elements
        const menuToggle = document.getElementById('menu-toggle');
        const menuClose = document.getElementById('menu-close');
        const mobileMenu = document.getElementById('mobile-menu');
        const servicesToggle = document.querySelector('.mobile-services-toggle');
        const servicesItem = document.querySelector('.mobile-services-item');
        const menuButton = document.querySelector('.mobile-menu-btn');
        
        console.log('🔥 MASTER MOBILE NAVBAR: Elements found:', {
            menuToggle: !!menuToggle,
            menuClose: !!menuClose,
            mobileMenu: !!mobileMenu,
            servicesToggle: !!servicesToggle,
            servicesItem: !!servicesItem,
            menuButton: !!menuButton
        });
        
        if (!menuToggle || !menuClose || !mobileMenu) {
            console.warn('🔥 MASTER MOBILE NAVBAR: Required elements not found');
            return;
        }
        
        // Ensure hamburger button is visible and clickable
        if (menuButton) {
            menuButton.style.display = 'flex';
            menuButton.style.alignItems = 'center';
            menuButton.style.justifyContent = 'center';
            menuButton.style.cursor = 'pointer';
            menuButton.style.pointerEvents = 'auto';
            menuButton.style.visibility = 'visible';
            menuButton.style.opacity = '1';
            menuButton.style.zIndex = '1001';
        }
        
        // Remove any existing event listeners by cloning elements
        const newMenuToggle = menuToggle.cloneNode(true);
        const newMenuClose = menuClose.cloneNode(true);
        
        if (menuToggle.parentNode) {
            menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);
        }
        if (menuClose.parentNode) {
            menuClose.parentNode.replaceChild(newMenuClose, menuClose);
        }
        
        // Get fresh references
        const freshMenuToggle = document.getElementById('menu-toggle');
        const freshMenuClose = document.getElementById('menu-close');
        
        // Function to open mobile menu
        function openMobileMenu() {
            console.log('🔥 MASTER MOBILE NAVBAR: Opening menu');
            if (mobileMenu) {
                mobileMenu.classList.add('active');
                mobileMenu.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // Force reflow and animate
                mobileMenu.offsetHeight;
                setTimeout(() => {
                    mobileMenu.style.top = '0';
                }, 10);
            }
        }
        
        // Function to close mobile menu
        function closeMobileMenu() {
            console.log('🔥 MASTER MOBILE NAVBAR: Closing menu');
            if (mobileMenu) {
                mobileMenu.style.top = '-100%';
                document.body.style.overflow = 'auto';
                
                // Close services dropdown when menu closes
                if (servicesItem) {
                    servicesItem.classList.remove('active');
                }
                
                setTimeout(() => {
                    mobileMenu.classList.remove('active');
                    mobileMenu.style.display = 'none';
                }, 400);
            }
        }
        
        // Menu toggle click
        if (freshMenuToggle) {
            freshMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔥 MASTER MOBILE NAVBAR: Hamburger clicked!');
                openMobileMenu();
            });
            
            // Also add to parent button
            if (menuButton) {
                menuButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔥 MASTER MOBILE NAVBAR: Button clicked!');
                    openMobileMenu();
                });
            }
        }
        
        // Menu close click
        if (freshMenuClose) {
            freshMenuClose.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔥 MASTER MOBILE NAVBAR: Close clicked!');
                closeMobileMenu();
            });
        }
        
        // Services dropdown toggle
        if (servicesToggle && servicesItem) {
            servicesToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔥 MASTER MOBILE NAVBAR: Services toggle clicked');
                servicesItem.classList.toggle('active');
            });
        }
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (mobileMenu && mobileMenu.classList.contains('active')) {
                if (!mobileMenu.contains(e.target) && !menuButton.contains(e.target)) {
                    closeMobileMenu();
                }
            }
        });
        
        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
        
        // Close menu when clicking on nav links (except services)
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-links a:not(.mobile-services-toggle)');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                console.log('🔥 MASTER MOBILE NAVBAR: Nav link clicked');
                closeMobileMenu();
            });
        });
        
        // Set active link based on current page
        setActiveLink();
        
        console.log('🔥 MASTER MOBILE NAVBAR: Initialized successfully!');
    }
    
    function setActiveLink() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.mobile-nav-links a, .mobile-services-dropdown a');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href.includes(currentPage)) {
                link.classList.add('active');
            }
        });
    }
    
    // Initialize with multiple methods for reliability
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMasterMobileNavbar);
    } else {
        initMasterMobileNavbar();
    }
    
    // Also run after a short delay to ensure everything is loaded
    setTimeout(initMasterMobileNavbar, 500);
    
    // Export for global access
    window.initMasterMobileNavbar = initMasterMobileNavbar;
    
})();
