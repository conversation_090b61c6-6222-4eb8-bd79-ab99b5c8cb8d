/* Study Page Sections Styling */

/* Quick Assessment Form */
.quick-assessment-section {
    background: #f0f9ff;
    padding: 40px 0;
    margin-top: 40px;
}

.assessment-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 30px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.1);
}

.assessment-header {
    text-align: center;
    margin-bottom: 30px;
}

.assessment-header h3 {
    color: #000080;
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.assessment-header p {
    color: #64748b;
}

.quick-assessment-form .form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.form-group {
    margin-bottom: 0;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    outline: none;
}

.assessment-submit {
    width: 100%;
    padding: 12px;
    background: #000080;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.assessment-submit:hover {
    background: #00005a;
    transform: translateY(-2px);
}

/* Study Opportunity Section */
.study-opportunity-section {
    padding: 80px 0;
    background: white;
}

.study-opportunity-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.section-header p {
    color: #64748b;
    font-size: 1.1rem;
}

.study-destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.study-destination-card {
    background: #f8fafc;
    border-radius: 15px;
    padding: 30px;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.study-destination-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.destination-flag {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
    border: 3px solid #f0f9ff;
}

.destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.study-destination-card h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
}

.feature-tag {
    background: #f0f9ff;
    color: #3b82f6;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.study-destination-card p {
    color: #64748b;
    margin-bottom: 20px;
}

.destination-btn {
    display: inline-block;
    background: #3b82f6;
    color: white;
    padding: 10px 20px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.destination-btn:hover {
    background: #1e3a8a;
    transform: translateY(-3px);
}

/* Footer Styles */
.footer-section {
    background: url(images/hero/Footer.jpg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    position: relative;
}

.footer-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7); /* Dark overlay for text readability */
    z-index: 1;
}

.footer-section .main-footer {
    position: relative;
    z-index: 2;
}

.footer-section .copyright {
    position: relative;
    z-index: 2;
}

/* Responsive styles */
@media (max-width: 992px) {
    .quick-assessment-form .form-grid {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .quick-assessment-form .form-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
}