/* Enhanced Destinations Section Styles */
.destinations-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f7fa 50%, #f0f9ff 100%);
    position: relative;
    overflow: hidden;
}

.destinations-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M30,30 Q50,10 70,30 Q90,50 70,70 Q50,90 30,70 Q10,50 30,30" fill="%23000080" opacity="0.02"/></svg>') repeat;
}

.destinations-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.destinations-header {
    text-align: center;
    margin-bottom: 60px;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.destination-card {
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 2px solid transparent;
}

.destination-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.destination-image {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.destination-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.destination-card:hover .destination-image img {
    transform: scale(1.1);
}

.destination-flag {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 30px;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 2px solid #fff;
    transition: all 0.3s ease;
}

.destination-card:hover .destination-flag {
    transform: scale(1.1) rotate(5deg);
}

.destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.destination-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.destination-name {
    font-size: 22px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.destination-card:hover .destination-name {
    color: var(--primary-dark);
}

.destination-info {
    margin-bottom: 20px;
    flex-grow: 1;
}

.destination-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.destination-feature i {
    color: var(--accent);
    font-size: 16px;
}

.destination-feature span {
    font-size: 14px;
    color: var(--text-dark);
}

.destination-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: var(--primary);
    font-weight: 700;
    text-decoration: none;
    font-size: 15px;
    transition: all 0.3s ease;
    margin-top: auto;
}

.destination-link:hover {
    gap: 10px;
    color: var(--primary-light);
}

.destination-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(0, 0, 128, 0.8);
    color: #fff;
    padding: 5px 12px;
    border-radius: 30px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 5px;
}

.destination-badge i {
    font-size: 14px;
}

/* Animation for cards */
.destination-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.destination-card.animated {
    opacity: 1;
    transform: translateY(0);
}

.destination-card:nth-child(1) { transition-delay: 0.1s; }
.destination-card:nth-child(2) { transition-delay: 0.2s; }
.destination-card:nth-child(3) { transition-delay: 0.3s; }
.destination-card:nth-child(4) { transition-delay: 0.4s; }
.destination-card:nth-child(5) { transition-delay: 0.5s; }
.destination-card:nth-child(6) { transition-delay: 0.6s; }
.destination-card:nth-child(7) { transition-delay: 0.7s; }
.destination-card:nth-child(8) { transition-delay: 0.8s; }

/* Responsive styles */
@media (max-width: 992px) {
    .destinations-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .destinations-section {
        padding: 70px 0;
    }
    
    .destinations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .destination-image {
        height: 160px;
    }
    
    .destination-content {
        padding: 20px;
    }
    
    .destination-name {
        font-size: 20px;
    }
}

@media (max-width: 576px) {
    .destinations-grid {
        grid-template-columns: 1fr;
        max-width: 350px;
        margin: 0 auto;
    }
    
    .destination-image {
        height: 200px;
    }
}