/* Hamburger <PERSON><PERSON> Fix - Clean & Professional */

/* Reset any conflicting styles */
.mobile-menu-btn {
    all: unset;
    box-sizing: border-box;
}

/* Desktop - Hide hamburger */
@media (min-width: 769px) {
    .mobile-menu-btn {
        display: none !important;
    }
}

/* Mobile - Show hamburger */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background: transparent;
        border: none;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1001;
        padding: 0;
        margin: 0;
        outline: none;
        -webkit-tap-highlight-color: transparent;
    }

    .mobile-menu-btn:hover {
        background: rgba(104, 127, 229, 0.1);
        transform: scale(1.05);
    }

    .mobile-menu-btn:active {
        transform: scale(0.95);
        background: rgba(104, 127, 229, 0.2);
    }

    .mobile-menu-btn i {
        font-size: 1.8rem !important;
        color: #687FE5 !important;
        transition: all 0.3s ease;
        display: block;
        line-height: 1;
        pointer-events: none;
    }

    /* Ensure hamburger is clickable */
    .mobile-menu-btn * {
        pointer-events: none;
    }

    /* Fix for touch devices */
    .mobile-menu-btn {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        touch-action: manipulation;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .mobile-menu-btn {
        width: 45px;
        height: 45px;
    }
    
    .mobile-menu-btn i {
        font-size: 1.6rem !important;
    }
}

/* Ensure hamburger button is always visible and clickable */
.mobile-menu-btn {
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}
