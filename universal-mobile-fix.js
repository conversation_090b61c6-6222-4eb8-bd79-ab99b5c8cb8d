// UNIVERSAL MOBILE NAVBAR FIX - All Pages
(function() {
    'use strict';
    
    console.log('🔧 UNIVERSAL MOBILE FIX: Loading...');
    
    function initUniversalMobileFix() {
        console.log('🔧 UNIVERSAL MOBILE FIX: Initializing...');
        
        const menuToggle = document.getElementById('menu-toggle');
        const menuClose = document.getElementById('menu-close');
        const mobileMenu = document.getElementById('mobile-menu');
        const servicesToggle = document.querySelector('.mobile-services-toggle');
        const servicesItem = document.querySelector('.mobile-services-item');
        
        console.log('🔧 Elements found:', {
            menuToggle: !!menuToggle,
            menuClose: !!menuClose,
            mobileMenu: !!mobileMenu,
            servicesToggle: !!servicesToggle,
            servicesItem: !!servicesItem
        });
        
        if (!menuToggle || !menuClose || !mobileMenu) {
            console.warn('🔧 Required elements not found');
            return;
        }
        
        // Remove existing listeners
        const newMenuToggle = menuToggle.cloneNode(true);
        const newMenuClose = menuClose.cloneNode(true);
        
        if (menuToggle.parentNode) {
            menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);
        }
        if (menuClose.parentNode) {
            menuClose.parentNode.replaceChild(newMenuClose, menuClose);
        }
        
        const freshMenuToggle = document.getElementById('menu-toggle');
        const freshMenuClose = document.getElementById('menu-close');
        
        function openMobileMenu() {
            console.log('🔧 Opening mobile menu');
            mobileMenu.classList.add('active');
            mobileMenu.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            setTimeout(() => {
                mobileMenu.style.top = '0';
            }, 10);
        }
        
        function closeMobileMenu() {
            console.log('🔧 Closing mobile menu');
            mobileMenu.style.top = '-100%';
            document.body.style.overflow = 'auto';
            
            if (servicesItem) {
                servicesItem.classList.remove('active');
            }
            
            setTimeout(() => {
                mobileMenu.classList.remove('active');
                mobileMenu.style.display = 'none';
            }, 400);
        }
        
        // Menu toggle
        if (freshMenuToggle) {
            freshMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                openMobileMenu();
            });
        }
        
        // Menu close
        if (freshMenuClose) {
            freshMenuClose.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeMobileMenu();
            });
        }
        
        // Services dropdown
        if (servicesToggle && servicesItem) {
            servicesToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                servicesItem.classList.toggle('active');
            });
        }
        
        // Close on outside click
        document.addEventListener('click', function(e) {
            if (mobileMenu && mobileMenu.classList.contains('active')) {
                if (!mobileMenu.contains(e.target)) {
                    closeMobileMenu();
                }
            }
        });
        
        // Close on escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
        
        // Close on nav link click
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-links a:not(.mobile-services-toggle)');
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMobileMenu();
            });
        });
        
        // Set active link
        setActiveLink();
        
        console.log('🔧 UNIVERSAL MOBILE FIX: Initialized successfully!');
    }
    
    function setActiveLink() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.mobile-nav-links a, .mobile-services-dropdown a');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href && href.includes(currentPage)) {
                link.classList.add('active');
            }
        });
    }
    
    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initUniversalMobileFix);
    } else {
        initUniversalMobileFix();
    }
    
    setTimeout(initUniversalMobileFix, 100);
    
})();
