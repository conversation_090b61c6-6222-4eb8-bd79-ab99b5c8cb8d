/* Visit Codex CSS - Based on HTMLCodex Template */
:root {
    --primary: #000080;
    --primary-light: #1e40af;
    --primary-dark: #000066;
    --secondary: #f59e0b;
    --accent: #10b981;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --bg-light: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #000080 0%, #1e40af 100%);
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Codex Hero Section */
.codex-hero {
    position: relative;
    background: linear-gradient(rgba(0, 0, 128, 0.9), rgba(30, 64, 175, 0.85)), url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 120px 0;
    color: #fff;
    overflow: hidden;
}

.codex-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></svg>') repeat;
    opacity: 0.3;
}

.codex-container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
    z-index: 1;
}

.codex-hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
}

.codex-hero-text {
    flex: 1;
    max-width: 600px;
}

.codex-hero-text h5 {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.5s ease-out;
}

.codex-hero-text h1 {
    font-size: 48px;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
    animation: fadeInUp 0.5s ease-out 0.1s both;
}

.codex-hero-text p {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
    animation: fadeInUp 0.5s ease-out 0.2s both;
}

.codex-hero-btns {
    display: flex;
    gap: 15px;
    animation: fadeInUp 0.5s ease-out 0.3s both;
}

.codex-btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: #fff;
    color: var(--primary);
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.codex-btn-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.codex-btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.codex-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.codex-hero-image {
    flex: 1;
    position: relative;
    animation: fadeInRight 0.5s ease-out 0.4s both;
}

.codex-hero-img {
    width: 100%;
    max-width: 500px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
    border: 5px solid rgba(255, 255, 255, 0.1);
}

.codex-hero-img:hover {
    transform: perspective(1000px) rotateY(0deg);
}

.codex-hero-badge {
    position: absolute;
    bottom: -20px;
    right: 30px;
    background: #fff;
    color: var(--primary);
    padding: 15px 25px;
    border-radius: 15px;
    font-weight: 700;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: fadeInUp 0.5s ease-out 0.6s both;
}

.codex-hero-badge i {
    font-size: 24px;
    color: var(--secondary);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* About Section */
.codex-about {
    padding: 100px 0;
    background-color: #fff;
}

.codex-about-content {
    display: flex;
    align-items: center;
    gap: 50px;
}

.codex-about-img {
    flex: 1;
    position: relative;
}

.codex-about-img img {
    width: 100%;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
}

.codex-about-img::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    width: 100px;
    height: 100px;
    background-color: var(--primary);
    opacity: 0.1;
    border-radius: 20px;
    z-index: -1;
}

.codex-about-img::after {
    content: '';
    position: absolute;
    bottom: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background-color: var(--secondary);
    opacity: 0.1;
    border-radius: 20px;
    z-index: -1;
}

.codex-about-text {
    flex: 1;
}

.codex-section-title {
    margin-bottom: 30px;
}

.codex-section-title h6 {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    color: var(--primary);
}

.codex-section-title h2 {
    font-size: 36px;
    font-weight: 800;
    color: var(--primary);
    line-height: 1.2;
}

.codex-about-text p {
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 30px;
}

.codex-about-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.codex-feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.codex-feature-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: var(--primary);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.codex-feature-text h5 {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
}

.codex-feature-text p {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-light);
    margin-bottom: 0;
}

/* Services Section */
.codex-services {
    padding: 100px 0;
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.codex-services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23000080" opacity="0.05"/></svg>') repeat;
}

.codex-services-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 60px;
}

.codex-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.codex-service-card {
    background: #fff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 128, 0.1);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.codex-service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.codex-service-card:hover::before {
    transform: scaleX(1);
}

.codex-service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.codex-service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    transition: var(--transition);
}

.codex-service-card:hover .codex-service-icon {
    transform: rotateY(180deg);
}

.codex-service-icon i {
    font-size: 30px;
    color: #fff;
    transition: var(--transition);
}

.codex-service-card:hover .codex-service-icon i {
    transform: rotateY(180deg);
}

.codex-service-content h4 {
    font-size: 22px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 15px;
}

.codex-service-content p {
    font-size: 15px;
    line-height: 1.7;
    color: var(--text-light);
    margin-bottom: 20px;
}

.codex-service-features {
    margin-bottom: 25px;
    flex-grow: 1;
}

.codex-service-feature {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.codex-service-feature i {
    color: var(--accent);
    font-size: 16px;
}

.codex-service-feature span {
    font-size: 14px;
    color: var(--text-dark);
}

.codex-service-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: var(--primary);
    font-weight: 700;
    text-decoration: none;
    font-size: 15px;
    transition: var(--transition);
}

.codex-service-link:hover {
    gap: 10px;
    color: var(--primary-light);
}

/* Process Section */
.codex-process {
    padding: 100px 0;
    background-color: #fff;
}

.codex-process-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 60px;
}

.codex-process-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
}

.codex-process-steps::before {
    content: '';
    position: absolute;
    top: 40px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: rgba(0, 0, 128, 0.1);
    z-index: 0;
}

.codex-process-step {
    position: relative;
    z-index: 1;
    text-align: center;
    width: 20%;
}

.codex-step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    font-weight: 800;
    margin: 0 auto 20px;
    position: relative;
    box-shadow: var(--shadow-md);
}

.codex-step-number::before {
    content: '';
    position: absolute;
    inset: -5px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    opacity: 0.3;
    z-index: -1;
}

.codex-step-content h4 {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 10px;
}

.codex-step-content p {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-light);
}

/* Testimonials Section */
.codex-testimonials {
    padding: 100px 0;
    background-color: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.codex-testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M30,30 Q50,10 70,30 Q90,50 70,70 Q50,90 30,70 Q10,50 30,30" fill="%23000080" opacity="0.03"/></svg>') repeat;
}

.codex-testimonials-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 60px;
}

.codex-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.codex-testimonial-card {
    background: #fff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 128, 0.1);
    position: relative;
}

.codex-testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.codex-testimonial-quote {
    font-size: 50px;
    line-height: 1;
    color: var(--primary);
    opacity: 0.2;
    margin-bottom: 20px;
}

.codex-testimonial-text {
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-dark);
    margin-bottom: 25px;
    font-style: italic;
}

.codex-testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.codex-author-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
}

.codex-author-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.codex-author-info h5 {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
}

.codex-author-info p {
    font-size: 14px;
    color: var(--text-light);
    margin: 0;
}

.codex-testimonial-rating {
    display: flex;
    gap: 3px;
    margin-top: 5px;
}

.codex-testimonial-rating i {
    color: #FFD700;
    font-size: 14px;
}

/* FAQ Section */
.codex-faq {
    padding: 100px 0;
    background-color: #fff;
}

.codex-faq-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 60px;
}

.codex-faq-wrapper {
    max-width: 800px;
    margin: 0 auto;
}

.codex-accordion-item {
    background: #fff;
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 128, 0.1);
    overflow: hidden;
}

.codex-accordion-header {
    padding: 20px 25px;
    background-color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: var(--transition);
}

.codex-accordion-header:hover {
    background-color: rgba(0, 0, 128, 0.02);
}

.codex-accordion-header h5 {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin: 0;
}

.codex-accordion-header i {
    font-size: 20px;
    color: var(--primary);
    transition: var(--transition);
}

.codex-accordion-item.active .codex-accordion-header i {
    transform: rotate(180deg);
}

.codex-accordion-body {
    padding: 0 25px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.codex-accordion-item.active .codex-accordion-body {
    padding: 0 25px 20px;
    max-height: 500px;
}

.codex-accordion-body p {
    font-size: 15px;
    line-height: 1.7;
    color: var(--text-light);
    margin: 0;
}

/* CTA Section */
.codex-cta {
    padding: 100px 0;
    background: linear-gradient(rgba(0, 0, 128, 0.9), rgba(30, 64, 175, 0.85)), url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    color: #fff;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.codex-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></svg>') repeat;
    opacity: 0.3;
}

.codex-cta-content {
    position: relative;
    z-index: 1;
    max-width: 700px;
    margin: 0 auto;
}

.codex-cta-content h2 {
    font-size: 40px;
    font-weight: 800;
    margin-bottom: 20px;
}

.codex-cta-content p {
    font-size: 18px;
    line-height: 1.7;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* Contact Form Section */
.codex-contact {
    padding: 100px 0;
    background-color: var(--bg-light);
}

.codex-contact-content {
    display: flex;
    gap: 50px;
}

.codex-contact-info {
    flex: 1;
}

.codex-contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 30px;
}

.codex-contact-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: var(--primary);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.codex-contact-text h5 {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
}

.codex-contact-text p {
    font-size: 15px;
    line-height: 1.6;
    color: var(--text-light);
    margin-bottom: 0;
}

.codex-contact-form {
    flex: 1;
    background: #fff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: var(--shadow-md);
}

.codex-form-group {
    margin-bottom: 20px;
}

.codex-form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 8px;
}

.codex-form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(0, 0, 128, 0.2);
    border-radius: 8px;
    font-size: 15px;
    color: var(--text-dark);
    transition: var(--transition);
}

.codex-form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 0, 128, 0.1);
}

.codex-form-row {
    display: flex;
    gap: 20px;
}

.codex-form-row .codex-form-group {
    flex: 1;
}

.codex-form-submit {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
}

.codex-form-submit:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .codex-container {
        padding: 0 30px;
    }
    
    .codex-hero-text h1 {
        font-size: 42px;
    }
    
    .codex-section-title h2 {
        font-size: 32px;
    }
    
    .codex-cta-content h2 {
        font-size: 36px;
    }
}

@media (max-width: 992px) {
    .codex-hero-content {
        flex-direction: column;
    }
    
    .codex-hero-text {
        text-align: center;
        max-width: 100%;
    }
    
    .codex-hero-btns {
        justify-content: center;
    }
    
    .codex-hero-image {
        margin-top: 40px;
        display: flex;
        justify-content: center;
    }
    
    .codex-hero-img {
        max-width: 400px;
    }
    
    .codex-about-content {
        flex-direction: column;
    }
    
    .codex-about-img {
        margin-bottom: 40px;
    }
    
    .codex-process-steps {
        flex-direction: column;
        align-items: center;
        gap: 40px;
    }
    
    .codex-process-steps::before {
        display: none;
    }
    
    .codex-process-step {
        width: 100%;
        max-width: 300px;
    }
    
    .codex-contact-content {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .codex-hero {
        padding: 80px 0;
    }
    
    .codex-hero-text h1 {
        font-size: 36px;
    }
    
    .codex-hero-text p {
        font-size: 16px;
    }
    
    .codex-hero-btns {
        flex-direction: column;
        gap: 15px;
    }
    
    .codex-btn-primary, .codex-btn-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .codex-hero-badge {
        position: static;
        margin: 30px auto 0;
        max-width: 250px;
    }
    
    .codex-section-title h2 {
        font-size: 28px;
    }
    
    .codex-about-features {
        grid-template-columns: 1fr;
    }
    
    .codex-services, .codex-process, .codex-testimonials, .codex-faq, .codex-cta, .codex-contact {
        padding: 60px 0;
    }
    
    .codex-cta-content h2 {
        font-size: 30px;
    }
    
    .codex-cta-content p {
        font-size: 16px;
    }
    
    .codex-form-row {
        flex-direction: column;
        gap: 0;
    }
}

@media (max-width: 576px) {
    .codex-hero-text h1 {
        font-size: 30px;
    }
    
    .codex-section-title h2 {
        font-size: 24px;
    }
    
    .codex-service-card {
        padding: 20px;
    }
    
    .codex-service-icon {
        width: 60px;
        height: 60px;
    }
    
    .codex-service-icon i {
        font-size: 24px;
    }
    
    .codex-service-content h4 {
        font-size: 20px;
    }
    
    .codex-testimonial-card {
        padding: 20px;
    }
    
    .codex-accordion-header {
        padding: 15px 20px;
    }
    
    .codex-accordion-header h5 {
        font-size: 16px;
    }
    
    .codex-accordion-item.active .codex-accordion-body {
        padding: 0 20px 15px;
    }
    
    .codex-cta-content h2 {
        font-size: 26px;
    }
}