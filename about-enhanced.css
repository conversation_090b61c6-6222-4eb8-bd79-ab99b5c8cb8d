/* About Enhanced Section CSS */
:root {
    --primary: #000080;
    --primary-light: #1e40af;
    --primary-dark: #000066;
    --secondary: #f59e0b;
    --accent: #10b981;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --bg-light: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #000080 0%, #1e40af 100%);
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* About Enhanced Section */
.about-enhanced {
    padding: 100px 0;
    background: #fff;
    position: relative;
    overflow: hidden;
}

.about-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23000080" opacity="0.03"/></svg>') repeat;
    z-index: 0;
}

.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.about-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.about-image {
    flex: 1;
    position: relative;
}

.about-img-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.about-img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    transition: var(--transition);
}

.about-img:hover {
    transform: scale(1.05);
}

.about-img-container::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    opacity: 0.1;
    border-radius: 20px;
    z-index: -1;
}

.about-img-container::after {
    content: '';
    position: absolute;
    bottom: -20px;
    right: -20px;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--secondary), var(--accent));
    opacity: 0.1;
    border-radius: 20px;
    z-index: -1;
}

.about-badge {
    position: absolute;
    bottom: 30px;
    left: 30px;
    background: #fff;
    color: var(--primary);
    padding: 15px 25px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 14px;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: fadeInUp 0.5s ease-out 0.6s both;
    border: 2px solid rgba(0, 0, 128, 0.1);
}

.about-badge i {
    font-size: 18px;
    color: var(--secondary);
}

.about-text {
    flex: 1;
}

.about-subtitle {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    color: var(--primary);
}

.about-title {
    font-size: 36px;
    font-weight: 800;
    color: var(--primary);
    line-height: 1.2;
    margin-bottom: 20px;
}

.about-description {
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 40px;
}

.about-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-bottom: 40px;
}

.about-feature {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: rgba(0, 0, 128, 0.02);
    border-radius: 15px;
    border: 1px solid rgba(0, 0, 128, 0.1);
    transition: var(--transition);
}

.about-feature:hover {
    background: rgba(0, 0, 128, 0.05);
    transform: translateY(-5px);
    box-shadow: var(--shadow-sm);
}

.about-feature-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: var(--shadow-sm);
}

.about-feature-text h5 {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 8px;
}

.about-feature-text p {
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-light);
    margin: 0;
}

.about-action {
    margin-top: 30px;
}

.about-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.about-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    gap: 15px;
}

.about-btn i {
    transition: var(--transition);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 992px) {
    .about-content {
        flex-direction: column;
        gap: 40px;
    }
    
    .about-image {
        order: 2;
    }
    
    .about-text {
        order: 1;
        text-align: center;
    }
    
    .about-title {
        font-size: 32px;
    }
}

@media (max-width: 768px) {
    .about-enhanced {
        padding: 60px 0;
    }
    
    .about-container {
        padding: 0 15px;
    }
    
    .about-title {
        font-size: 28px;
    }
    
    .about-features {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .about-img {
        height: 300px;
    }
    
    .about-badge {
        bottom: 20px;
        left: 20px;
        padding: 12px 20px;
        font-size: 12px;
    }
}

@media (max-width: 576px) {
    .about-title {
        font-size: 24px;
    }
    
    .about-feature {
        padding: 15px;
    }
    
    .about-feature-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        font-size: 16px;
    }
    
    .about-feature-text h5 {
        font-size: 16px;
    }
    
    .about-btn {
        width: 100%;
        justify-content: center;
    }
}
