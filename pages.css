/* About Page Styles */
.about-hero-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    padding: 120px 0 80px;
    text-align: center;
    color: white;
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text-content {
    text-align: left;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.hero-badge i {
    font-size: 1.2rem;
}

.about-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.hero-actions {
    display: flex;
    gap: 20px;
    margin-top: 30px;
    justify-content: center; /* Center align the buttons */
    width: 100%; /* Ensure full width */
}

.btn-primary, .btn-secondary {
    padding: 14px 24px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.6);
}

.btn-secondary {
    background: white;
    color: #1e3a8a;
    border: 2px solid #e5e7eb;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
    background: #f8fafc;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.hero-image {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.overlay-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 50px;
    color: white;
    font-weight: 600;
}

.overlay-badge i {
    font-size: 1.2rem;
}

/* Company Introduction Section */
.company-intro-section {
    padding: 100px 0;
    background: white;
}

.intro-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.intro-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #f0f9ff;
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.section-badge i {
    font-size: 1.2rem;
}

.intro-text h2 {
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 30px;
    line-height: 1.2;
}

.intro-description {
    color: #64748b;
    margin-bottom: 20px;
    line-height: 1.7;
}

.visual-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.visual-item {
    background: #f8fafc;
    border-radius: 15px;
    padding: 30px;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.visual-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.visual-icon {
    font-size: 2rem;
    color: #3b82f6;
    margin-bottom: 20px;
}

.visual-item h4 {
    color: #1e293b;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.visual-item p {
    color: #64748b;
    line-height: 1.6;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .about-hero-content,
    .intro-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .hero-text-content {
        text-align: center;
    }
    
    .about-hero-content h1 {
        font-size: 2.2rem;
    }
    
    .visual-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}