/* Service Cards Fix - One card per line */
.services-content {
    display: flex;
    align-items: flex-start;
}

.services-left, .services-right {
    display: flex;
    flex-direction: column;
}

/* Make right side match left side height */
.services-right {
    max-height: 400px;
    overflow-y: auto;
}

/* Custom scrollbar for the services list */
.services-right::-webkit-scrollbar {
    width: 5px;
}

.services-right::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 5px;
}

.services-right::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 5px;
}

.services-right::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

.service-highlight, .services-list {
    display: flex;
    flex-direction: column;
}

.services-list {
    gap: 3px;
}

/* Reduce height by making elements more compact */
.service-item {
    padding: 5px;
    margin-bottom: 0;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.service-content p {
    font-size: 0.75rem;
    line-height: 1.2;
    margin-bottom: 0;
    max-height: 1.2em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    color: #64748b;
}

.service-content h4 {
    font-size: 0.9rem;
    margin-bottom: 2px;
    margin-top: 0;
}

.service-icon-small {
    width: 28px;
    height: 28px;
    min-width: 28px;
    font-size: 0.8rem;
    border-radius: 5px;
}

/* Make left side more compact */
.service-highlight h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.service-highlight p {
    margin-bottom: 15px;
}

.service-stats {
    margin-bottom: 15px;
}

.service-features {
    margin-bottom: 15px;
}

.feature-point {
    margin-bottom: 5px;
}

.service-item {
    width: 100%;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 128, 0.1);
}

.service-icon-small {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: linear-gradient(135deg, #000080, #1e40af);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.service-content {
    flex: 1;
}

.service-content h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #1e293b;
    font-size: 1.2rem;
}

.service-content p {
    margin: 0;
    color: #64748b;
    line-height: 1.6;
}