.university-placements-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    position: relative;
    z-index: auto;
    overflow: visible;
}

.university-placements-section::before {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(0, 0, 128, 0.03);
    z-index: 1;
}

.university-placements-section::after {
    content: "";
    position: absolute;
    bottom: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: rgba(59, 130, 246, 0.03);
    z-index: 1;
}

.placements-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

.placements-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

.placements-header .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #f0f9ff;
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.placements-header .section-badge i {
    font-size: 1.2rem;
}

.placements-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.placements-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.universities-by-country {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    position: relative;
    z-index: 1;
}

.country-universities {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.country-universities::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #000080, #3b82f6);
}

.country-universities:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 128, 0.1);
}

.country-universities h3 {
    color: #000080;
    font-size: 1.5rem;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
    position: relative;
    display: flex;
    align-items: center;
    gap: 15px;
}

.country-universities h3::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: #3b82f6;
}

.country-flag {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e2e8f0;
    display: inline-block;
}

.country-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.university-logos {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
}

.university-logo {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    border-radius: 10px;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.university-logo:hover {
    background: #f0f9ff;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.05);
}

.university-logo img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: contain;
    border: 2px solid #e2e8f0;
    background: white;
    padding: 5px;
    transition: all 0.3s ease;
}

.university-logo:hover img {
    border-color: #3b82f6;
    transform: scale(1.05);
}

.university-logo span {
    font-weight: 600;
    color: #334155;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.university-logo:hover span {
    color: #000080;
}

@media (max-width: 992px) {
    .universities-by-country {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .university-logos {
        grid-template-columns: 1fr;
    }
}