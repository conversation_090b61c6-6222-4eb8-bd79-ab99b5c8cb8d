/* Testimonials Section Styles */
.testimonials-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f7fa 50%, #f0f9ff 100%);
    position: relative;
    overflow: hidden;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 Q50,10 80,20 Q90,50 80,80 Q50,90 20,80 Q10,50 20,20" fill="%23000080" opacity="0.02"/></svg>') repeat;
}

.testimonials-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.testimonials-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    padding-top: 20px;
}

/* Badge styling moved to badge-fix.css */

.testimonials-header h2 {
    font-size: 36px;
    font-weight: 500;
    color: #000080;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.testimonials-header h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #000080, #1e40af);
    border-radius: 3px;
}

.testimonials-slider {
    position: relative;
    padding: 20px 0;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 128, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 128, 0.2);
}

.quote-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 40px;
    color: rgba(0, 0, 128, 0.1);
    line-height: 1;
}

.stars {
    display: flex;
    margin-bottom: 15px;
    color: #f59e0b;
    font-size: 18px;
}

.testimonial-text {
    color: #64748b;
    line-height: 1.7;
    font-size: 16px;
    margin-bottom: 20px;
    flex-grow: 1;
}

.testimonial-text.truncated {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.read-more-btn {
    background: none;
    border: none;
    color: #000080;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    margin-bottom: 20px;
    font-size: 14px;
    text-decoration: underline;
    align-self: flex-start;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: auto;
}

.author-info {
    display: flex;
    flex-direction: column;
}

.author-info h4 {
    font-size: 16px;
    font-weight: 700;
    color: #000080;
    margin: 0 0 5px;
}

.author-info span {
    font-size: 14px;
    color: #64748b;
}

/* Swiper Styles */
.swiper {
    width: 100%;
    padding-bottom: 50px;
}

.swiper-slide {
    height: auto;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .testimonials-section {
        padding: 70px 0;
    }
    
    .testimonials-header h2 {
        font-size: 28px;
    }
    
    .testimonial-card {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .testimonials-section {
        padding: 50px 0;
    }
    
    .testimonials-header h2 {
        font-size: 24px;
    }
    
    .testimonial-card {
        padding: 20px;
    }
    
    .stars {
        font-size: 16px;
    }
    
    .testimonial-text {
        font-size: 14px;
    }
}