/* Modern Study Process Section */
.study-process-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    position: relative;
    overflow: hidden;
}

.study-process-section::before {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(0, 0, 128, 0.03);
    z-index: 1;
}

.study-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.study-process-section .section-header {
    text-align: center;
    margin-bottom: 70px;
}

.study-process-section .section-header h2 {
    font-size: 2.8rem;
    color: #000080;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.study-process-section .section-header h2::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #3b82f6;
}

.study-process-section .section-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.process-timeline {
    position: relative;
    max-width: 900px;
    margin: 0 auto;
}

.process-timeline::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    background: linear-gradient(to bottom, #000080, #3b82f6);
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    display: flex;
    justify-content: flex-end;
    padding-right: 30px;
    position: relative;
    margin-bottom: 50px;
    width: 50%;
}

.timeline-item:nth-child(even) {
    align-self: flex-end;
    justify-content: flex-start;
    padding-right: 0;
    padding-left: 30px;
    margin-left: 50%;
}

.timeline-item::before {
    content: "";
    position: absolute;
    top: 20px;
    right: -13px;
    width: 26px;
    height: 26px;
    background: white;
    border: 4px solid #000080;
    border-radius: 50%;
    z-index: 1;
}

.timeline-item:nth-child(even)::before {
    right: auto;
    left: -13px;
}

.timeline-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.1);
    position: relative;
    width: 100%;
    max-width: 400px;
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.15);
}

.timeline-step {
    position: absolute;
    top: -15px;
    left: 30px;
    background: #000080;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.2);
}

.timeline-content h3 {
    font-size: 1.5rem;
    color: #000080;
    margin: 15px 0;
}

.timeline-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Responsive styles */
@media (max-width: 768px) {
    .process-timeline::before {
        left: 30px;
    }
    
    .timeline-item {
        width: 100%;
        padding-right: 0;
        padding-left: 80px;
        justify-content: flex-start;
    }
    
    .timeline-item:nth-child(even) {
        margin-left: 0;
        padding-left: 80px;
    }
    
    .timeline-item::before,
    .timeline-item:nth-child(even)::before {
        left: 17px;
        right: auto;
    }
    
    .timeline-content {
        max-width: 100%;
    }
}