/* Professional Study Opportunity Cards */
.study-opportunity-section {
    padding: 80px 0 120px;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    position: relative;
    overflow: visible;
    z-index: auto;
    margin-bottom: 30px;
}

.study-opportunity-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.study-opportunity-container::after {
    content: "";
    display: table;
    clear: both;
}

.study-opportunity-section .section-header {
    text-align: center;
    margin-bottom: 50px;
}

.study-opportunity-section .section-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.study-opportunity-section .section-header h2::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #3b82f6;
}

.study-opportunity-section .section-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.study-destinations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    position: relative;
    z-index: 1;
    overflow: visible;
}

.destination-card {
    background: white;
    border-radius: 15px;
    overflow: visible;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 128, 0.1);
}

.destination-header {
    position: relative;
    height: 150px;
    overflow: visible;
}

.destination-header > img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
    overflow: hidden;
}

.destination-card:hover .destination-header img {
    transform: scale(1.1);
}

.destination-flag {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.destination-content {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.destination-card h3 {
    font-size: 1.3rem;
    color: #000080;
    margin-bottom: 10px;
    position: relative;
    padding-bottom: 10px;
}

.destination-card h3::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: #3b82f6;
}

.destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 10px;
}

.feature-tag {
    background: #f0f9ff;
    color: #3b82f6;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

.destination-card p {
    color: #64748b;
    margin-bottom: 10px;
    line-height: 1.4;
    flex-grow: 1;
    font-size: 0.9rem;
}

.destination-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-top: 15px;
    border-top: 1px solid #f0f4f8;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #000080;
}

.stat-label {
    font-size: 0.8rem;
    color: #64748b;
}

.destination-btn {
    display: inline-block;
    background: #000080;
    color: white;
    padding: 10px 20px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 0.9rem;
    margin-top: 5px;
}

.destination-btn i {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.destination-btn:hover {
    background: #3b82f6;
}

.destination-btn:hover i {
    transform: translateX(5px);
}

/* Responsive styles */
@media (max-width: 992px) {
    .study-destinations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .study-destinations-grid {
        grid-template-columns: 1fr;
    }
    
    .study-opportunity-section .section-header h2 {
        font-size: 2rem;
    }
}