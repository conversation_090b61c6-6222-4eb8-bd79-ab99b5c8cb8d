// Mobile Navigation Fix - Simple and Direct
document.addEventListener('DOMContentLoaded', function() {
    console.log('Mobile Nav Fix: Initializing...');
    
    // Get elements
    const menuToggle = document.getElementById('menu-toggle');
    const menuClose = document.getElementById('menu-close');
    const mobileMenu = document.getElementById('mobile-menu');
    const menuButton = document.querySelector('.mobile-menu-btn');
    
    console.log('Mobile Nav Fix: Elements found:', {
        menuToggle: !!menuToggle,
        menuClose: !!menuClose,
        mobileMenu: !!mobileMenu,
        menuButton: !!menuButton
    });
    
    // Open mobile menu
    function openMobileMenu() {
        console.log('Mobile Nav Fix: Opening menu');
        mobileMenu.classList.add('active');
        mobileMenu.style.display = 'block';
        mobileMenu.style.top = '0';
        document.body.style.overflow = 'hidden';
    }
    
    // Close mobile menu
    function closeMobileMenu() {
        console.log('Mobile Nav Fix: Closing menu');
        mobileMenu.style.top = '-100%';
        document.body.style.overflow = 'auto';
        
        setTimeout(() => {
            mobileMenu.classList.remove('active');
            mobileMenu.style.display = 'none';
        }, 300);
    }
    
    // Add event listeners
    if (menuToggle) {
        menuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openMobileMenu();
        });
    }
    
    if (menuButton) {
        menuButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openMobileMenu();
        });
    }
    
    if (menuClose) {
        menuClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeMobileMenu();
        });
    }
    
    // Toggle services dropdown
    const servicesToggle = document.querySelector('.mobile-services-toggle');
    const servicesItem = document.querySelector('.mobile-services-item');
    
    if (servicesToggle && servicesItem) {
        servicesToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            servicesItem.classList.toggle('active');
        });
    }
    
    // Close menu when clicking on links
    const mobileLinks = document.querySelectorAll('.mobile-nav-links a, .mobile-services-dropdown a');
    mobileLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });
    
    console.log('Mobile Nav Fix: Initialization complete');
});