/* Enhanced Destination Features Styling */
.destination-feature {
    position: relative;
    padding: 12px 15px;
    margin-bottom: 10px;
    background: rgba(0, 0, 128, 0.03);
    border-radius: 10px;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.destination-feature:hover {
    background: rgba(0, 0, 128, 0.06);
    transform: translateX(5px);
    border-left: 3px solid var(--primary);
}

.destination-feature i {
    position: relative;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.destination-feature:hover i {
    transform: scale(1.2);
    box-shadow: 0 3px 10px rgba(0, 0, 128, 0.2);
}

.destination-feature span {
    font-weight: 500;
    font-size: 14px;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.destination-feature:hover span {
    color: var(--primary);
    font-weight: 600;
}

/* All icons use navy blue color */
.destination-feature i {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
}

/* Feature count badge */
.destination-info {
    position: relative;
}

.feature-count {
    position: absolute;
    top: -10px;
    right: 0;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 128, 0.2);
}

/* Feature tooltip */
.destination-feature {
    position: relative;
}

.feature-tooltip {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%) scale(0.8);
    background: var(--primary);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.2);
}

.feature-tooltip::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px 5px 0;
    border-style: solid;
    border-color: var(--primary) transparent transparent;
}

.destination-feature:hover .feature-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) scale(1);
}

/* Responsive styles */
@media (max-width: 768px) {
    .destination-feature {
        padding: 10px 12px;
    }
    
    .destination-feature i {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }
    
    .destination-feature span {
        font-size: 13px;
    }
}