/* MASTER MOBILE NAVBAR CSS - Universal for All Pages */

/* Desktop Menu - Default Visible */
.desktop-menu {
    display: block;
}

/* Mobile Menu Button - Hidden by Default */
.mobile-menu-btn {
    display: none;
    cursor: pointer;
    font-size: 1.8rem;
    color: #687FE5;
    padding: 10px;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: transparent;
    border: none;
    position: relative;
    z-index: 1001;
}

.mobile-menu-btn:hover {
    background: rgba(104, 127, 229, 0.1);
    transform: scale(1.05);
}

/* Mobile Menu Container */
.mobile-menu {
    display: none;
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10001;
    transition: top 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    top: 0;
    display: block;
}

/* Mobile Menu Header */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 25px;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.mobile-logo {
    height: 70px;
    filter: drop-shadow(2px 2px 8px rgba(0, 0, 0, 0.2));
}

#menu-close {
    font-size: 1.5rem;
    color: #687FE5;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: rgba(104, 127, 229, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#menu-close:hover {
    background: rgba(104, 127, 229, 0.2);
    transform: rotate(90deg);
}

/* Mobile Navigation Links */
.mobile-nav-links {
    list-style: none;
    padding: 30px 0;
    margin: 0;
    background: white;
}

.mobile-nav-links li {
    margin: 0;
}

.mobile-nav-links li a {
    display: block;
    padding: 22px 25px;
    text-decoration: none;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: transparent;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 10px 20px;
}

.mobile-nav-links li a:hover,
.mobile-nav-links li a.active {
    background: #687FE5;
    color: white;
    transform: translateX(5px);
}

/* Services Dropdown */
.mobile-services-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 22px 25px;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin: 10px 20px;
}

.mobile-services-toggle:hover {
    background: #687FE5;
    color: white;
}

.mobile-services-dropdown {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
    margin: 0 20px;
    border-radius: 8px;
}

.mobile-services-item.active .mobile-services-dropdown {
    max-height: 300px;
    padding: 10px 0;
}

.mobile-services-dropdown a {
    display: block;
    padding: 15px 25px 15px 40px;
    text-decoration: none;
    color: #555;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    margin: 5px 15px;
    border-radius: 6px;
}

.mobile-services-dropdown a:hover {
    background: #687FE5;
    color: white;
    transform: translateX(5px);
}

.dropdown-arrow {
    transition: transform 0.3s ease;
}

.mobile-services-item.active .dropdown-arrow {
    transform: rotate(180deg);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .desktop-menu {
        display: none !important;
    }
    
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
    }
    
    .mobile-menu {
        display: block;
    }
}

/* Extra Small Mobile */
@media (max-width: 480px) {
    .mobile-menu-header {
        padding: 20px 15px;
    }
    
    .mobile-logo {
        height: 60px;
    }
    
    .mobile-nav-links li a,
    .mobile-services-toggle {
        padding: 18px 20px;
        font-size: 1rem;
        margin: 8px 15px;
    }
    
    .mobile-services-dropdown a {
        padding: 12px 20px 12px 30px;
        font-size: 0.9rem;
        margin: 4px 10px;
    }
}
