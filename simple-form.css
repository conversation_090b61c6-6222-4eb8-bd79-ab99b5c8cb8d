/* Simple Assessment Form Styles */
.assessment-section {
    padding: 80px 0;
    background: #f8fafc;
    position: relative;
    z-index: auto;
    margin-bottom: 40px;
}

.assessment-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

.assessment-header {
    text-align: center;
    margin-bottom: 40px;
}

.assessment-header .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #e6f0ff;
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.assessment-header .section-badge i {
    font-size: 1.2rem;
}

.assessment-header h3 {
    font-size: 2.2rem;
    color: #000080;
    margin-bottom: 15px;
}

.assessment-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.assessment-form {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.08);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px 20px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    color: #334155;
    background: #f8fafc;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input::placeholder {
    color: #94a3b8;
}

.assessment-submit {
    width: 100%;
    padding: 15px;
    background: #000080;
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1.05rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.assessment-submit i {
    transition: all 0.3s ease;
}

.assessment-submit:hover {
    background: #3b82f6;
}

.assessment-submit:hover i {
    transform: translateX(5px);
}

@media (max-width: 992px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .assessment-section {
        padding: 40px 0;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .assessment-header h3 {
        font-size: 1.5rem;
    }
}