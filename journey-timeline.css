/* Professional Journey Timeline Styles */
.journey-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    position: relative;
    overflow: hidden;
}

.journey-section::before {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(0, 0, 128, 0.03);
    z-index: 1;
}

.journey-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.journey-header {
    text-align: center;
    margin-bottom: 70px;
}

.journey-header .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #f0f9ff;
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.journey-header .section-badge i {
    font-size: 1.2rem;
}

.journey-header h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.journey-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.timeline-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-center-line {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    background: linear-gradient(to bottom, #000080, #3b82f6);
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    display: flex;
    margin-bottom: 80px;
    position: relative;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item:nth-child(odd) {
    justify-content: flex-end;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: auto;
}

.timeline-content {
    width: 45%;
    position: relative;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.08);
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.12);
}

.timeline-item:nth-child(odd) .timeline-content::before {
    content: "";
    position: absolute;
    top: 30px;
    right: -15px;
    width: 30px;
    height: 30px;
    background: white;
    transform: rotate(45deg);
    box-shadow: 5px -5px 10px rgba(0, 0, 0, 0.05);
}

.timeline-item:nth-child(even) .timeline-content::before {
    content: "";
    position: absolute;
    top: 30px;
    left: -15px;
    width: 30px;
    height: 30px;
    background: white;
    transform: rotate(45deg);
    box-shadow: -5px 5px 10px rgba(0, 0, 0, 0.05);
}

.timeline-dot {
    position: absolute;
    top: 30px;
    left: 50%;
    width: 40px;
    height: 40px;
    background: white;
    border: 4px solid #000080;
    border-radius: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    box-shadow: 0 0 0 5px rgba(0, 0, 128, 0.1);
}

.timeline-dot span {
    font-weight: 700;
    color: #000080;
}

.timeline-content h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.timeline-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 15px;
}

.timeline-icon {
    width: 60px;
    height: 60px;
    background: #f0f9ff;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.timeline-icon i {
    font-size: 1.8rem;
    color: #000080;
}

.timeline-features {
    margin-top: 20px;
}

.timeline-feature {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.timeline-feature i {
    color: #3b82f6;
    margin-right: 10px;
}

.timeline-feature span {
    color: #64748b;
}

/* Responsive styles */
@media (max-width: 992px) {
    .timeline-center-line {
        left: 40px;
    }
    
    .timeline-item {
        justify-content: flex-start;
        margin-left: 80px;
    }
    
    .timeline-item:nth-child(odd) {
        justify-content: flex-start;
    }
    
    .timeline-content {
        width: 100%;
    }
    
    .timeline-dot {
        left: 40px;
    }
    
    .timeline-item:nth-child(odd) .timeline-content::before,
    .timeline-item:nth-child(even) .timeline-content::before {
        left: -15px;
        right: auto;
        top: 30px;
    }
}

@media (max-width: 768px) {
    .journey-header h2 {
        font-size: 2rem;
    }
    
    .timeline-content {
        padding: 20px;
    }
}